'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Locale } from '../../../../lib/i18n';
import { getTranslation } from '../../../../lib/translations';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';
import WhatsAppButton from '../../../../components/WhatsAppButton';
import { Product, Category, Subcategory } from '../../../../src/types/database';
import { productsApi, categoriesApi, subcategoriesApi } from '../../../../src/lib/api';

export default function ProductDetailPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  const id = (params?.id || '') as string;
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [showToast, setShowToast] = useState(false);
  const [whatsappNumber, setWhatsappNumber] = useState('+966501234567');

  const t = (key: string) => getTranslation(locale, key as any);

  useEffect(() => {
    if (id) {
      fetchProductDetails(id);
    }
  }, [id]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showToast) {
      timer = setTimeout(() => setShowToast(false), 3000);
    }
    return () => clearTimeout(timer);
  }, [showToast]);

  useEffect(() => {
    // قراءة رقم الواتساب من الإعدادات
    const savedSettings = localStorage.getItem('siteSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        if (settings.communicationSettings?.whatsapp?.businessNumber) {
          setWhatsappNumber(settings.communicationSettings.whatsapp.businessNumber);
        } else if (settings.whatsappNumber) {
          // للتوافق مع النسخة القديمة
          setWhatsappNumber(settings.whatsappNumber);
        }
      } catch (error) {
        console.log('Using default WhatsApp number');
      }
    }
  }, []);

  const fetchProductDetails = async (productId: string) => {
    try {
      setLoading(true);
      const productData = await productsApi.getById(productId);
      setProduct(productData);

      if (productData.categoryId) {
        const categoryData = await categoriesApi.getById(productData.categoryId);
        setCategory(categoryData);
      }
      
      if (productData.subcategoryId) {
        const subcategoryData = await subcategoriesApi.getById(productData.subcategoryId);
        setSubcategory(subcategoryData);
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = () => {
    if (!product || !product.available) return;

    const cartItem = {
      id: product.id,
      title: locale === 'ar' ? product.titleAr : product.title,
      image: product.images[0] || 'https://via.placeholder.com/400x300',
      price: product.price,
      quantity: quantity
    };

    const existingCart = localStorage.getItem('cart');
    let cart = existingCart ? JSON.parse(existingCart) : [];

    const existingItemIndex = cart.findIndex((item: any) => item.id === product.id);

    if (existingItemIndex > -1) {
      cart[existingItemIndex].quantity += quantity;
    } else {
      cart.push(cartItem);
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    setShowToast(true);
  };

  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">{t('productDetails.loading')}</p>
          </div>
        </div>
        <Footer locale={locale} />
      </>
    );
  }

  if (!product) {
    return (
      <>
        <Navbar locale={locale} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">{t('productDetails.notFound')}</h1>
            <button
              onClick={() => router.push(`/${locale}/products`)}
              className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('productDetails.backToProducts')}
            </button>
          </div>
        </div>
        <Footer locale={locale} />
      </>
    );
  }

  const productTitle = locale === 'ar' ? product.titleAr : product.title;
  const productDescription = locale === 'ar' ? product.descriptionAr : product.description;
  const productFeatures = locale === 'ar' ? product.featuresAr : product.features;
  const productSpecs = product.specifications;

  return (
    <>
      <Navbar locale={locale} />
      
      <main className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-8">
            <a href={`/${locale}`} className="hover:text-primary">{t('home')}</a>
            <span>/</span>
            <a href={`/${locale}/products`} className="hover:text-primary">{t('products')}</a>
            {category && (
              <>
                <span>/</span>
                <span className="hover:text-primary">
                  {locale === 'ar' ? category.nameAr : category.name}
                </span>
              </>
            )}
            {subcategory && (
              <>
                <span>/</span>
                <span className="hover:text-primary">
                  {locale === 'ar' ? subcategory.nameAr : subcategory.name}
                </span>
              </>
            )}
            <span>/</span>
            <span className="text-primary font-medium">{productTitle}</span>
          </nav>

          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
              {/* Product Images */}
              <div className="space-y-4">
                <div className="aspect-square rounded-xl overflow-hidden bg-gray-100">
                  <img
                    src={product.images[selectedImageIndex] || 'https://via.placeholder.com/600x600'}
                    alt={productTitle}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {product.images.length > 1 && (
                  <div className="flex space-x-2 space-x-reverse overflow-x-auto">
                    {product.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                          selectedImageIndex === index ? 'border-primary' : 'border-gray-200'
                        }`}
                      >
                        <img
                          src={image || 'https://via.placeholder.com/80x80'}
                          alt={`${productTitle} ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Info */}
              <div className="space-y-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">{productTitle}</h1>
                  
                  {/* Category & Subcategory */}
                  <div className="flex items-center space-x-2 space-x-reverse mt-4">
                    {category && (
                      <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                        {locale === 'ar' ? category.nameAr : category.name}
                      </span>
                    )}
                    {subcategory && (
                      <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                        {locale === 'ar' ? subcategory.nameAr : subcategory.name}
                      </span>
                    )}
                  </div>
                </div>

                {/* Price */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <span className="text-3xl font-bold text-primary">{product.price} {t('currency')}</span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="text-xl text-gray-500 line-through">{product.originalPrice} {t('currency')}</span>
                  )}
                </div>

                {/* Availability */}
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className={`w-3 h-3 rounded-full ${product.available ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className={`font-medium ${product.available ? 'text-green-600' : 'text-red-600'}`}>
                    {product.available ? t('available') : t('unavailable')}
                  </span>
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">{t('productDetails.description')}</h3>
                  <p className="text-gray-600 leading-relaxed">{productDescription}</p>
                </div>

                {/* Features */}
                {productFeatures && productFeatures.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">{t('productDetails.features')}</h3>
                    <ul className="space-y-2">
                      {productFeatures.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2 space-x-reverse">
                          <i className="ri-check-line text-green-500"></i>
                          <span className="text-gray-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Specifications */}
                {productSpecs && productSpecs.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">{t('productDetails.specifications')}</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <dl className="space-y-2">
                        {productSpecs.map((spec, index) => (
                          <div key={index} className="flex justify-between">
                            <dt className="font-medium text-gray-700">
                              {locale === 'ar' ? spec.nameAr : spec.nameEn}:
                            </dt>
                            <dd className="text-gray-600">
                              {locale === 'ar' ? spec.valueAr : spec.valueEn}
                            </dd>
                          </div>
                        ))}
                      </dl>
                    </div>
                  </div>
                )}

                {/* Quantity Selector */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <span className="font-medium text-gray-700">{t('quantity')}:</span>
                  <div className="flex items-center border border-gray-300 rounded-lg">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <i className="ri-subtract-line"></i>
                    </button>
                    <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <i className="ri-add-line"></i>
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="pt-6 border-t space-y-3">
                  <button
                    onClick={addToCart}
                    disabled={!product.available}
                    className={`w-full py-4 px-6 rounded-xl font-bold text-lg flex items-center justify-center space-x-2 space-x-reverse transition-all duration-300 ${
                      product.available
                        ? 'bg-primary hover:bg-primary/90 text-white hover:shadow-lg transform hover:-translate-y-0.5'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <i className="ri-shopping-cart-2-line text-xl"></i>
                    <span>{product.available ? t('addToCart') : t('unavailable')}</span>
                  </button>
                  
                  <a
                    href={`https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${t('whatsapp.message')} ${productTitle}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-xl font-bold text-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors"
                  >
                    <i className="ri-whatsapp-line text-xl"></i>
                    <span>
                      {locale === 'ar' ? 'تواصل معنا عبر واتساب' : 'Contact us via WhatsApp'}
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2 space-x-reverse animate-slide-up">
          <i className="ri-check-line text-xl"></i>
          <span>{t('cartMessages.addedToCart')}</span>
        </div>
      )}
    </>
  );
}
