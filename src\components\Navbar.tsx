import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslation } from 'next-i18next';
import ClientOnly from './ClientOnly';
import LanguageSwitcher from './LanguageSwitcher';

const Navbar = () => {
  const { t } = useTranslation('common');
  const [cartCount, setCartCount] = useState(0);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // تحديث العدد عند تحميل الصفحة أو تغيير السلة
    const updateCartCount = () => {
      const cart = localStorage.getItem('cart');
      if (cart) {
        try {
          const items = JSON.parse(cart);
          setCartCount(items.reduce((sum: number, item: any) => sum + (item.quantity || 1), 0));
        } catch {
          setCartCount(0);
        }
      } else {
        setCartCount(0);
      }
    };
    updateCartCount();
    window.addEventListener('storage', updateCartCount);
    // دعم التحديث عند إضافة منتج من نفس الصفحة
    window.addEventListener('cartUpdated', updateCartCount);
    return () => {
      window.removeEventListener('storage', updateCartCount);
      window.removeEventListener('cartUpdated', updateCartCount);
    };
  }, []);

  // النسخة المبسطة للـ SSR
  const SimpleNavbar = () => (
    <header className="bg-gradient-to-r from-white to-gray-50 shadow-lg sticky top-0 z-50 border-b border-gray-100">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            <Link href="/" className="text-3xl font-['Pacifico'] text-primary">
              DROOB HAJER
            </Link>
            <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
              <Link href="/" className="text-primary font-semibold">الرئيسية</Link>
              <Link href="/products" className="text-gray-700 font-semibold">المنتجات</Link>
              <Link href="/about" className="text-gray-700 font-semibold">من نحن</Link>
              <Link href="/contact" className="text-gray-700 font-semibold">اتصل بنا</Link>
            </nav>
          </div>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2 px-3 py-2 text-gray-700">
              <span className="font-medium">العربية</span>
              <div className="w-4 h-4 flex items-center justify-center">
                <i className="ri-arrow-down-s-line"></i>
              </div>
            </div>
            <Link href="/cart" className="relative group">
              <div className="w-12 h-12 flex items-center justify-center text-gray-700 bg-gray-100 rounded-full">
                <i className="ri-shopping-cart-2-line text-xl"></i>
              </div>
            </Link>
            <button className="md:hidden w-12 h-12 flex items-center justify-center text-gray-700 bg-gray-100 rounded-full">
              <i className="ri-menu-line text-xl"></i>
            </button>
          </div>
        </div>
      </div>
    </header>
  );

  // النسخة التفاعلية للعميل
  const InteractiveNavbar = () => {
    return (
      <header className="bg-gradient-to-r from-white to-gray-50 shadow-lg sticky top-0 z-50 border-b border-gray-100">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-8">
              <Link href="/" className="text-3xl font-['Pacifico'] text-primary hover:text-secondary transition-colors duration-300 transform hover:scale-105">
                DROOB HAJER
              </Link>
              <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
                <Link href="/" className="relative text-primary font-semibold hover:text-secondary transition-all duration-300 group">
                  {t('nav.home')}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
                </Link>
                <Link href="/products" className="relative text-gray-700 font-semibold hover:text-primary transition-all duration-300 group">
                  {t('nav.products')}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
                </Link>
                <Link href="/about" className="relative text-gray-700 font-semibold hover:text-primary transition-all duration-300 group">
                  {t('nav.about')}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
                </Link>
                <Link href="/contact" className="relative text-gray-700 font-semibold hover:text-primary transition-all duration-300 group">
                  {t('nav.contact')}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
                </Link>
              </nav>
            </div>
            <div className="flex items-center gap-6">
              <LanguageSwitcher />
              <Link href="/cart" className="relative group">
                <div className="w-12 h-12 flex items-center justify-center text-gray-700 hover:text-primary bg-gray-100 hover:bg-primary/10 rounded-full transition-all duration-300 transform group-hover:scale-110">
                  <i className="ri-shopping-cart-2-line text-xl"></i>
                </div>
                {isMounted && cartCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-6 h-6 flex items-center justify-center rounded-full font-bold shadow-lg animate-pulse">
                    {cartCount}
                  </span>
                )}
              </Link>
              <button
                className="md:hidden w-12 h-12 flex items-center justify-center text-gray-700 hover:text-primary bg-gray-100 hover:bg-primary/10 rounded-full transition-all duration-300"
                onClick={() => setMobileMenuOpen((v) => !v)}
                aria-label="فتح القائمة الجانبية"
              >
                <i className={`ri-${mobileMenuOpen ? 'close' : 'menu'}-line text-xl transition-transform duration-300`}></i>
              </button>
            </div>
          </div>
          <div className={`md:hidden mt-4 transition-all duration-300 overflow-hidden ${mobileMenuOpen ? 'max-h-64 opacity-100' : 'max-h-0 opacity-0'}`} id="mobile-menu">
            <nav className="flex flex-col space-y-2 bg-gray-50 rounded-lg p-4">
              <Link href="/" className="text-primary font-semibold py-3 px-4 rounded-lg hover:bg-white transition-colors duration-200">{t('nav.home')}</Link>
              <Link href="/products" className="text-gray-700 font-semibold py-3 px-4 rounded-lg hover:bg-white hover:text-primary transition-colors duration-200">{t('nav.products')}</Link>
              <Link href="/about" className="text-gray-700 font-semibold py-3 px-4 rounded-lg hover:bg-white hover:text-primary transition-colors duration-200">{t('nav.about')}</Link>
              <Link href="/contact" className="text-gray-700 font-semibold py-3 px-4 rounded-lg hover:bg-white hover:text-primary transition-colors duration-200">{t('nav.contact')}</Link>
            </nav>
          </div>
        </div>
      </header>
    );
  };

  return (
    <ClientOnly>
      {InteractiveNavbar()}
    </ClientOnly>
  );
};

export default Navbar;
