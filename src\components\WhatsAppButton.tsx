import React from 'react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

const WhatsAppButton = () => {
  const { t } = useTranslation('common');
  const router = useRouter();

  const handleClick = () => {
    const message = encodeURIComponent(t('whatsapp.message'));
    const whatsappUrl = `https://wa.me/966500000000?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <button
      onClick={handleClick}
      className="fixed bottom-6 right-6 w-14 h-14 bg-green-600 rounded-full flex items-center justify-center shadow-lg hover:bg-green-700 transition-colors z-50"
      aria-label={t('whatsapp.tooltip')}
      title={t('whatsapp.tooltip')}
    >
      <i className="ri-whatsapp-line text-3xl text-white"></i>
    </button>
  );
};

export default WhatsAppButton;
