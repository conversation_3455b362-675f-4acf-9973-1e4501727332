

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import ClientOnly from './ClientOnly';

interface ProductCardProps {
  id: number;
  image: string;
  title: string;
  description: string;
  price?: number;
  available?: boolean;
  alt?: string;
  locale: 'ar' | 'en';
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  image,
  title,
  description,
  price = 0,
  available = true,
  alt,
  locale
}) => {
  const [showToast, setShowToast] = useState(false);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showToast) {
      timer = setTimeout(() => setShowToast(false), 3000);
    }
    return () => clearTimeout(timer);
  }, [showToast]);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!available) return;

    const cartItem = {
      id: id.toString(),
      title: title,
      image: image,
      price: price,
      quantity: 1
    };

    // جلب السلة الحالية من localStorage
    const existingCart = localStorage.getItem('cart');
    let cart = existingCart ? JSON.parse(existingCart) : [];

    // البحث عن المنتج في السلة
    const existingItemIndex = cart.findIndex((item: any) => item.id === id.toString());

    if (existingItemIndex > -1) {
      // إذا كان المنتج موجود، زيادة الكمية
      cart[existingItemIndex].quantity += 1;
    } else {
      // إذا لم يكن موجود، إضافته للسلة
      cart.push(cartItem);
    }

    // حفظ السلة المحدثة
    localStorage.setItem('cart', JSON.stringify(cart));

    // إرسال حدث لتحديث عداد السلة
    window.dispatchEvent(new Event('cartUpdated'));

    setShowToast(true);
  };

  // النسخة المبسطة للـ SSR
  const SimpleCard = () => (
    <div className="product-card bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="block relative">
        <div className="relative overflow-hidden">
          <img src={image} alt={alt || title} className="w-full h-64 object-cover object-top" />
          {!available && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <span className="bg-red-500 text-white px-3 py-1 rounded-lg font-bold text-sm">غير متوفر</span>
            </div>
          )}
        </div>
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
        <p className="text-gray-600 text-sm mb-4 leading-relaxed">{description}</p>

        {price > 0 && (
          <div className="mb-4">
            <span className="text-2xl font-bold text-primary">{price} ر.س</span>
          </div>
        )}

        <div className="flex justify-between items-center gap-3">
          <span className="text-primary font-semibold">عرض التفاصيل</span>
          <span className={`px-6 py-2 rounded-lg text-sm font-semibold ${
            available
              ? 'bg-primary text-white'
              : 'bg-gray-300 text-gray-500'
          }`}>
            {available ? 'إضافة للسلة' : 'غير متوفر'}
          </span>
        </div>
      </div>
    </div>
  );

  // النسخة التفاعلية للعميل
  const InteractiveCard = () => (
    <>
      {showToast && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300">
          <div className="flex items-center gap-2">
            <i className="ri-check-line"></i>
            تم إضافة {title} إلى السلة
          </div>
        </div>
      )}
      <div className="product-card bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <Link href={`/${locale}/product/${id}`} className="block relative">
          <div className="relative overflow-hidden">
            <img src={image} alt={alt || title} className="w-full h-64 object-cover object-top transition-transform duration-300 group-hover:scale-105" />
            {!available && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <span className="bg-red-500 text-white px-3 py-1 rounded-lg font-bold text-sm">
                  {locale === 'ar' ? 'غير متوفر' : 'Unavailable'}
                </span>
              </div>
            )}
          </div>
          <div className="product-actions absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
            <button
              className={`w-12 h-12 rounded-full bg-white shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110 ${
                available
                  ? 'text-primary hover:bg-primary hover:text-white'
                  : 'text-gray-400 cursor-not-allowed'
              }`}
              title={available ? "إضافة إلى السلة" : "غير متوفر"}
              onClick={handleAddToCart}
              disabled={!available}
            >
              <i className="ri-shopping-cart-2-line text-lg"></i>
            </button>
            <a
              href={`https://wa.me/966500000000?text=مرحباً، أريد الاستفسار عن المنتج التالي:%0A%0A*${encodeURIComponent(title)}*%0A%0A${price > 0 ? `السعر: ${price} ريال%0A%0A` : ''}${encodeURIComponent(description)}%0A%0Aيرجى تزويدي بمزيد من التفاصيل والتوفر.`}
              className="w-12 h-12 rounded-full bg-white shadow-lg flex items-center justify-center text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300 transform hover:scale-110"
              title="طلب عبر واتساب"
              target="_blank"
              rel="noopener noreferrer"
              onClick={(e) => e.stopPropagation()}
            >
              <i className="ri-whatsapp-line text-lg"></i>
            </a>
            <a
              href={`/quote?id=${id}`}
              className="w-12 h-12 rounded-full bg-white shadow-lg flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-all duration-300 transform hover:scale-110"
              title="طلب عرض سعر"
              onClick={(e) => e.stopPropagation()}
            >
              <i className="ri-file-list-3-line text-lg"></i>
            </a>
          </div>
        </Link>
        <div className="p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">{title}</h3>
          <p className="text-gray-600 text-sm mb-4 leading-relaxed">{description}</p>

          {price > 0 && (
            <div className="mb-4">
              <span className="text-2xl font-bold text-primary">{price} ر.س</span>
            </div>
          )}

          <div className="flex justify-between items-center gap-3">
            <Link href={`/${locale}/product/${id}`} className="text-primary font-semibold hover:underline flex items-center gap-1 transition-colors duration-300">
              {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
              <i className="ri-arrow-left-line text-sm"></i>
            </Link>
            <button
              className={`px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 whitespace-nowrap ${
                available
                  ? 'bg-primary text-white hover:bg-primary/90 hover:shadow-lg transform hover:-translate-y-0.5'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              onClick={handleAddToCart}
              disabled={!available}
            >
              {available
                ? (locale === 'ar' ? 'إضافة للسلة' : 'Add to Cart')
                : (locale === 'ar' ? 'غير متوفر' : 'Unavailable')
              }
            </button>
          </div>
        </div>
      </div>
    </>
  );

  return (
    <ClientOnly fallback={<SimpleCard />}>
      <InteractiveCard />
    </ClientOnly>
  );
};

export default ProductCard;
