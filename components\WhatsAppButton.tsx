'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';

interface WhatsAppButtonProps {
  locale: Locale;
}

const WhatsAppButton: React.FC<WhatsAppButtonProps> = ({ locale }) => {
  const t = (key: string) => getTranslation(locale, key as any);

  const handleClick = () => {
    const message = encodeURIComponent(t('whatsapp.message'));
    const whatsappUrl = `https://wa.me/966123456789?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <button
      onClick={handleClick}
      className="fixed bottom-6 right-6 w-14 h-14 bg-green-600 rounded-full flex items-center justify-center shadow-lg hover:bg-green-700 transition-all duration-300 z-50 transform hover:scale-110"
      aria-label={locale === 'ar' ? 'تواصل عبر واتساب' : 'Contact via WhatsApp'}
      title={locale === 'ar' ? 'تواصل عبر واتساب' : 'Contact via WhatsApp'}
    >
      <i className="ri-whatsapp-line text-3xl text-white"></i>
    </button>
  );
};

export default WhatsAppButton;
