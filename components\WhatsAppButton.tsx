'use client';

import React, { useEffect, useState } from 'react';
import { Locale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';

interface WhatsAppButtonProps {
  locale: Locale;
}

const WhatsAppButton: React.FC<WhatsAppButtonProps> = ({ locale }) => {
  const t = (key: string) => getTranslation(locale, key as any);
  const [whatsappSettings, setWhatsappSettings] = useState({
    businessNumber: '+966501234567',
    welcomeMessage: 'Hello! How can we help you today?',
    welcomeMessageAr: 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
    enabled: true
  });

  useEffect(() => {
    // قراءة إعدادات الواتساب من localStorage
    const savedSettings = localStorage.getItem('siteSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        if (settings.communicationSettings?.whatsapp) {
          setWhatsappSettings(prev => ({
            ...prev,
            ...settings.communicationSettings.whatsapp
          }));
        } else if (settings.whatsappNumber) {
          // للتوافق مع النسخة القديمة
          setWhatsappSettings(prev => ({
            ...prev,
            businessNumber: settings.whatsappNumber
          }));
        }
      } catch (error) {
        console.log('Using default WhatsApp settings');
      }
    }
  }, []);

  const handleClick = () => {
    const message = locale === 'ar' ? whatsappSettings.welcomeMessageAr : whatsappSettings.welcomeMessage;
    const phoneNumber = whatsappSettings.businessNumber.replace(/[^0-9]/g, '');
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  // إخفاء الزر إذا كان الواتساب معطل
  if (!whatsappSettings.enabled) {
    return null;
  }

  return (
    <button
      onClick={handleClick}
      className="fixed bottom-6 right-6 w-14 h-14 bg-green-600 rounded-full flex items-center justify-center shadow-lg hover:bg-green-700 transition-all duration-300 z-50 transform hover:scale-110"
      aria-label={locale === 'ar' ? 'تواصل عبر واتساب' : 'Contact via WhatsApp'}
      title={locale === 'ar' ? 'تواصل عبر واتساب' : 'Contact via WhatsApp'}
    >
      <i className="ri-whatsapp-line text-3xl text-white"></i>
    </button>
  );
};

export default WhatsAppButton;
