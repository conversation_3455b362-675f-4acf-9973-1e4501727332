import React from 'react';
import Link from 'next/link';
import { useTranslation } from 'next-i18next';
import { useFooterSettings, useSocialLinks, useContactInfo, useSiteSettings } from '../hooks/useSiteSettings';

const Footer = () => {
  const { t } = useTranslation('common');
  const { footerSettings } = useFooterSettings();
  const { socialLinks } = useSocialLinks();
  const { contactInfo } = useContactInfo();
  const { settings } = useSiteSettings();

  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          <div>
            <h3 className="text-2xl font-['Pacifico'] mb-6">
              {settings?.siteName || 'DROOB HAJER'}
            </h3>
            <p className="text-gray-400 mb-6">
              {footerSettings?.companyInfo?.description || t('footer.description')}
            </p>
            {footerSettings?.showSocialLinks && (
              <div className="flex gap-4">
                {socialLinks?.facebook && (
                  <a href={socialLinks.facebook} target="_blank" rel="noopener noreferrer" className="w-10 h-10 rounded-full border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white hover:border-white transition-colors">
                    <i className="ri-facebook-fill"></i>
                  </a>
                )}
                {socialLinks?.twitter && (
                  <a href={socialLinks.twitter} target="_blank" rel="noopener noreferrer" className="w-10 h-10 rounded-full border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white hover:border-white transition-colors">
                    <i className="ri-twitter-x-fill"></i>
                  </a>
                )}
                {socialLinks?.instagram && (
                  <a href={socialLinks.instagram} target="_blank" rel="noopener noreferrer" className="w-10 h-10 rounded-full border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white hover:border-white transition-colors">
                    <i className="ri-instagram-fill"></i>
                  </a>
                )}
                {socialLinks?.linkedin && (
                  <a href={socialLinks.linkedin} target="_blank" rel="noopener noreferrer" className="w-10 h-10 rounded-full border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white hover:border-white transition-colors">
                    <i className="ri-linkedin-fill"></i>
                  </a>
                )}
                {socialLinks?.youtube && (
                  <a href={socialLinks.youtube} target="_blank" rel="noopener noreferrer" className="w-10 h-10 rounded-full border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white hover:border-white transition-colors">
                    <i className="ri-youtube-fill"></i>
                  </a>
                )}
              </div>
            )}
          </div>
          <div>
            <h3 className="text-xl font-bold mb-6">{t('footer.quickLinks')}</h3>
            <ul className="space-y-3">
              {footerSettings?.quickLinks?.filter(link => link.isActive).map((link, index) => (
                <li key={index}>
                  <Link href={link.url} className="text-gray-400 hover:text-white transition-colors">
                    {link.nameEn}
                  </Link>
                </li>
              )) || (
                <>
                  <li><Link href="/" className="text-gray-400 hover:text-white transition-colors">{t('footer.home')}</Link></li>
                  <li><Link href="/products" className="text-gray-400 hover:text-white transition-colors">{t('footer.products')}</Link></li>
                  <li><Link href="/about" className="text-gray-400 hover:text-white transition-colors">{t('footer.about')}</Link></li>
                  <li><Link href="/contact" className="text-gray-400 hover:text-white transition-colors">{t('footer.contact')}</Link></li>
                  <li><Link href="/quote" className="text-gray-400 hover:text-white transition-colors">{t('footer.quote')}</Link></li>
                </>
              )}
            </ul>
          </div>
          <div>
            <h3 className="text-xl font-bold mb-6">{t('footer.productCategories')}</h3>
            <ul className="space-y-3">
              <li><Link href="/products?category=kitchen" className="text-gray-400 hover:text-white transition-colors">{t('footer.kitchenEquipment')}</Link></li>
              <li><Link href="/products?category=rooms" className="text-gray-400 hover:text-white transition-colors">{t('footer.roomEquipment')}</Link></li>
              <li><Link href="/products?category=restaurant" className="text-gray-400 hover:text-white transition-colors">{t('footer.restaurantFurniture')}</Link></li>
              <li><Link href="/products?category=supplies" className="text-gray-400 hover:text-white transition-colors">{t('footer.hotelSupplies')}</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="text-xl font-bold mb-6">{t('footer.contactUs')}</h3>
            <ul className="space-y-4">
              {footerSettings?.companyInfo?.showAddress && contactInfo?.address && (
                <li className="flex gap-3">
                  <div className="w-5 h-5 flex items-center justify-center text-gray-400 mt-1"><i className="ri-map-pin-line"></i></div>
                  <span className="text-gray-400 whitespace-pre-line">{contactInfo.address}</span>
                </li>
              )}
              {footerSettings?.companyInfo?.showPhone && contactInfo?.phone && (
                <li className="flex gap-3">
                  <div className="w-5 h-5 flex items-center justify-center text-gray-400"><i className="ri-phone-line"></i></div>
                  <a href={`tel:${contactInfo.phone}`} className="text-gray-400 hover:text-white transition-colors">{contactInfo.phone}</a>
                </li>
              )}
              {footerSettings?.companyInfo?.showEmail && contactInfo?.email && (
                <li className="flex gap-3">
                  <div className="w-5 h-5 flex items-center justify-center text-gray-400"><i className="ri-mail-line"></i></div>
                  <a href={`mailto:${contactInfo.email}`} className="text-gray-400 hover:text-white transition-colors">{contactInfo.email}</a>
                </li>
              )}
              {footerSettings?.companyInfo?.showWorkingHours && contactInfo?.workingHours && (
                <li className="flex gap-3">
                  <div className="w-5 h-5 flex items-center justify-center text-gray-400 mt-1"><i className="ri-time-line"></i></div>
                  <span className="text-gray-400">{contactInfo.workingHours}</span>
                </li>
              )}
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 mb-4 md:mb-0">
              © 2025 {settings?.siteName || 'DROOB HAJER'}. {footerSettings?.copyrightText || t('footer.rights')}
            </p>
            <div className="flex gap-4">
              <div className="w-10 h-6 flex items-center justify-center"><i className="ri-visa-line ri-lg text-gray-400"></i></div>
              <div className="w-10 h-6 flex items-center justify-center"><i className="ri-mastercard-line ri-lg text-gray-400"></i></div>
              <div className="w-10 h-6 flex items-center justify-center"><i className="ri-paypal-line ri-lg text-gray-400"></i></div>
              <div className="w-10 h-6 flex items-center justify-center"><i className="ri-apple-fill ri-lg text-gray-400"></i></div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
