import { NextApiRequest, NextApiResponse } from 'next';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { customerInfo, products } = req.body;

      // إنشاء معرف فريد للطلب
      const requestId = `QR-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // حساب المجموع الكلي
      const totalAmount = products.reduce((total: number, item: CartItem) => 
        total + (item.price * item.quantity), 0
      );

      // إنشاء مجلد البيانات إذا لم يكن موجوداً
      const dataDir = path.join(process.cwd(), 'src', 'data');
      const quotesDir = path.join(dataDir, 'quotes');
      const excelDir = path.join(dataDir, 'excel');
      
      if (!fs.existsSync(quotesDir)) {
        fs.mkdirSync(quotesDir, { recursive: true });
      }
      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // إنشاء ملف Excel للمنتجات
      const excelFileName = `${requestId}.xlsx`;
      const excelFilePath = path.join(excelDir, excelFileName);
      
      // تحضير البيانات لملف Excel
      const excelData = products.map((product: CartItem, index: number) => ({
        'رقم المنتج': index + 1,
        'اسم المنتج': product.titleAr || product.title,
        'الكمية': product.quantity,
        'السعر المقترح': '', // سيتم ملؤه من قبل الإدارة
        'ملاحظات': ''
      }));

      // إنشاء workbook وworksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 10 }, // رقم المنتج
        { wch: 30 }, // اسم المنتج
        { wch: 10 }, // الكمية
        { wch: 15 }, // السعر المقترح
        { wch: 20 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'طلب التسعير');
      XLSX.writeFile(workbook, excelFilePath);

      // إنشاء كائن طلب التسعير
      const quoteRequest: QuoteRequest = {
        id: requestId,
        customerInfo,
        products,
        totalAmount,
        createdAt: new Date().toISOString(),
        status: 'pending',
        excelFilePath: `src/data/excel/${excelFileName}`
      };

      // حفظ بيانات الطلب في ملف JSON
      const jsonFilePath = path.join(quotesDir, `${requestId}.json`);
      fs.writeFileSync(jsonFilePath, JSON.stringify(quoteRequest, null, 2));

      // تحديث ملف الفهرس العام للطلبات
      const indexFilePath = path.join(quotesDir, 'index.json');
      let allRequests: QuoteRequest[] = [];
      
      if (fs.existsSync(indexFilePath)) {
        const indexContent = fs.readFileSync(indexFilePath, 'utf-8');
        allRequests = JSON.parse(indexContent);
      }
      
      allRequests.unshift(quoteRequest); // إضافة الطلب الجديد في المقدمة
      fs.writeFileSync(indexFilePath, JSON.stringify(allRequests, null, 2));

      res.status(200).json({
        success: true,
        message: 'تم إرسال طلب التسعير بنجاح',
        requestId,
        excelFilePath: quoteRequest.excelFilePath
      });

    } catch (error) {
      console.error('Error creating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء معالجة الطلب'
      });
    }
  } else if (req.method === 'GET') {
    try {
      const quotesDir = path.join(process.cwd(), 'src', 'data', 'quotes');
      const indexFilePath = path.join(quotesDir, 'index.json');
      
      if (!fs.existsSync(indexFilePath)) {
        return res.status(200).json({ success: true, requests: [] });
      }
      
      const indexContent = fs.readFileSync(indexFilePath, 'utf-8');
      const allRequests = JSON.parse(indexContent);
      
      res.status(200).json({ success: true, requests: allRequests });
      
    } catch (error) {
      console.error('Error fetching quote requests:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء جلب الطلبات'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
