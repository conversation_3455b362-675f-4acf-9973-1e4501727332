import React, { useState, useRef } from 'react';

interface ImageUploadProps {
  onImagesUploaded: (imageUrls: string[]) => void;
  multiple?: boolean;
  maxFiles?: number;
  currentImages?: string[];
  label?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImagesUploaded,
  multiple = false,
  maxFiles = 5,
  currentImages = [],
  label = 'اختر الصور'
}) => {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    
    // التحقق من عدد الملفات
    if (currentImages.length + fileArray.length > maxFiles) {
      alert(`يمكنك رفع ${maxFiles} صور كحد أقصى`);
      return;
    }

    // التحقق من نوع الملفات
    const validFiles = fileArray.filter(file => file.type.startsWith('image/'));
    if (validFiles.length !== fileArray.length) {
      alert('يرجى اختيار ملفات صور فقط');
      return;
    }

    // التحقق من حجم الملفات (10MB لكل ملف)
    const oversizedFiles = validFiles.filter(file => file.size > 10 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      alert('حجم الملف يجب أن يكون أقل من 10 ميجابايت');
      return;
    }

    await uploadFiles(validFiles);
  };

  const uploadFiles = async (files: File[]) => {
    setUploading(true);
    
    try {
      const formData = new FormData();
      files.forEach((file, index) => {
        formData.append(`file${index}`, file);
      });

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('فشل في رفع الصور');
      }

      const result = await response.json();
      
      if (result.success) {
        const newImages = multiple 
          ? [...currentImages, ...result.files]
          : result.files;
        onImagesUploaded(newImages);
      } else {
        throw new Error(result.error || 'فشل في رفع الصور');
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('حدث خطأ أثناء رفع الصور');
    } finally {
      setUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeImage = (index: number) => {
    const newImages = currentImages.filter((_, i) => i !== index);
    onImagesUploaded(newImages);
  };

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {multiple && `(حد أقصى ${maxFiles} صور)`}
      </label>
      
      {/* منطقة رفع الصور */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple={multiple}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
        
        {uploading ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
            <p className="text-gray-600">جاري رفع الصور...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <i className="ri-upload-cloud-2-line text-4xl text-gray-400 mb-2"></i>
            <p className="text-gray-600 mb-1">اسحب الصور هنا أو انقر للاختيار</p>
            <p className="text-sm text-gray-500">PNG, JPG, GIF حتى 10MB</p>
          </div>
        )}
      </div>

      {/* معاينة الصور المرفوعة */}
      {currentImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {currentImages.map((image, index) => (
            <div key={index} className="relative group">
              <img
                src={image}
                alt={`صورة ${index + 1}`}
                className="w-full h-24 object-cover rounded-lg border border-gray-200"
              />
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  removeImage(index);
                }}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
